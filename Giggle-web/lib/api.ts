// API service for communicating with <PERSON><PERSON><PERSON> backend
import { getTokenFromCookie } from './appwrite';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';

interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ApiService {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const token = getTokenFromCookie();
    
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // User Management APIs
  async registerUser(userData: {
    name: string;
    profile?: any;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/users/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getUserProfile(): Promise<ApiResponse> {
    return this.makeRequest('/api/users/profile', {
      method: 'GET',
    });
  }

  async updateUserProfile(userData: {
    name?: string;
    profile?: any;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/users/profile', {
      method: 'PATCH',
      body: JSON.stringify(userData),
    });
  }

  async deleteUserProfile(): Promise<ApiResponse> {
    return this.makeRequest('/api/users/profile', {
      method: 'DELETE',
    });
  }

  async sendVerificationEmail(): Promise<ApiResponse> {
    return this.makeRequest('/api/users/send-verification-email', {
      method: 'POST',
      body: JSON.stringify({}),
    });
  }

  // Seeker APIs
  async getSeekerProfile(): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/profile', {
      method: 'GET',
    });
  }

  async createSeekerProfile(seekerData: {
    name: string;
    phoneNumber?: string;
    educationLevel: string;
    employmentStatus: string;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/profile', {
      method: 'POST',
      body: JSON.stringify(seekerData),
    });
  }

  async updateSeekerProfile(seekerData: any): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/profile', {
      method: 'PATCH',
      body: JSON.stringify(seekerData),
    });
  }

  async updateSeekerBasicInfo(basicInfo: {
    name?: string;
    dateOfBirth?: string;
    gender?: string;
    phoneNumber?: string;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/onboarding/basic-info', {
      method: 'PATCH',
      body: JSON.stringify(basicInfo),
    });
  }

  async updateSeekerLocation(locationData: {
    preferences: any;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/onboarding/location', {
      method: 'PATCH',
      body: JSON.stringify(locationData),
    });
  }

  async updateSeekerEducation(educationData: {
    education: any[];
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/onboarding/education', {
      method: 'PATCH',
      body: JSON.stringify(educationData),
    });
  }

  async updateSeekerSkills(skillsData: {
    skills: any[];
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/onboarding/skills', {
      method: 'PATCH',
      body: JSON.stringify(skillsData),
    });
  }

  async uploadSeekerResume(resumeData: {
    resumeLink?: string;
    resumeFileId?: string;
  }): Promise<ApiResponse> {
    return this.makeRequest('/api/seeker/onboarding/resume', {
      method: 'PATCH',
      body: JSON.stringify(resumeData),
    });
  }

  // Provider APIs
  async getProviderGigs(): Promise<ApiResponse> {
    return this.makeRequest('/api/provider/gigs', {
      method: 'GET',
    });
  }

  async createGig(gigData: any): Promise<ApiResponse> {
    return this.makeRequest('/api/provider/gigs', {
      method: 'POST',
      body: JSON.stringify(gigData),
    });
  }

  async getGig(gigId: string): Promise<ApiResponse> {
    return this.makeRequest(`/api/provider/gigs/${gigId}`, {
      method: 'GET',
    });
  }

  async updateGig(gigId: string, gigData: any): Promise<ApiResponse> {
    return this.makeRequest(`/api/provider/gigs/${gigId}`, {
      method: 'PATCH',
      body: JSON.stringify(gigData),
    });
  }

  async deleteGig(gigId: string): Promise<ApiResponse> {
    return this.makeRequest(`/api/provider/gigs/${gigId}`, {
      method: 'DELETE',
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;

// Export types for use in components
export type { ApiResponse };
