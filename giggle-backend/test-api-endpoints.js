// Simple test script to verify API endpoints are working
// Run with: node test-api-endpoints.js

const BASE_URL = 'http://localhost:5000';

// Test function to make HTTP requests
async function testEndpoint(method, endpoint, data = null, token = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method: method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.text();
    
    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log(`Response: ${result}`);
    
    return { status: response.status, data: result };
  } catch (error) {
    console.log(`\n${method} ${endpoint}`);
    console.log(`Error: ${error.message}`);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Testing Giggle Backend API Endpoints');
  console.log('==========================================');

  // Test 1: Basic server health check (should return 404 for root)
  await testEndpoint('GET', '/');

  // Test 2: User profile without token (should return 401)
  await testEndpoint('GET', '/api/users/profile');

  // Test 3: User profile with invalid token (should return 401)
  await testEndpoint('GET', '/api/users/profile', null, 'invalid_token');

  // Test 4: User registration without token (should return 401)
  await testEndpoint('POST', '/api/users/register', {
    name: 'Test User',
    profile: { bio: 'Test bio' }
  });

  // Test 5: Seeker profile without token (should return 401)
  await testEndpoint('GET', '/api/seeker/profile');

  // Test 6: Provider gigs without token (should return 401)
  await testEndpoint('GET', '/api/provider/gigs');

  // Test 7: User verification endpoint (should work without token)
  await testEndpoint('GET', '/api/users/verify-user?userId=test&secret=test');

  console.log('\n✅ API Endpoint Tests Completed');
  console.log('\nNotes:');
  console.log('- 401 errors are expected for protected endpoints without valid tokens');
  console.log('- 404 errors are expected for non-existent endpoints');
  console.log('- To test with valid tokens, get JWT from frontend after login');
  console.log('\nNext Steps:');
  console.log('1. Import Postman collection: Giggle_Backend_API_Tests.postman_collection.json');
  console.log('2. Import Postman environment: Giggle_Backend_Environment.postman_environment.json');
  console.log('3. Get valid JWT token from frontend login');
  console.log('4. Update jwt_token variable in Postman environment');
  console.log('5. Test all endpoints with valid authentication');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  console.log('Alternative: Use curl commands or Postman for testing');
  
  console.log('\n📋 Sample curl commands:');
  console.log('# Test user profile (should return 401):');
  console.log('curl -X GET http://localhost:5000/api/users/profile');
  
  console.log('\n# Test with token (replace YOUR_JWT_TOKEN):');
  console.log('curl -X GET http://localhost:5000/api/users/profile \\');
  console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
  console.log('  -H "Content-Type: application/json"');
  
  console.log('\n# Test user registration (replace YOUR_JWT_TOKEN):');
  console.log('curl -X POST http://localhost:5000/api/users/register \\');
  console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -d \'{"name": "Test User", "profile": {"bio": "Test bio"}}\'');
  
  process.exit(1);
}

// Run the tests
runTests().catch(console.error);
