# Giggle Backend API Integration Guide

## Overview
This guide covers the integration between the Giggle frontend (Next.js) and backend (Express.js) APIs. The backend is currently running on `http://localhost:5000`.

## Current Status
✅ **Backend Server**: Running successfully on port 5000
✅ **Database**: MongoDB connection established
✅ **Authentication**: Appwrite integration working
✅ **API Endpoints**: All routes configured and accessible

## Frontend Integration Status

### Completed Frontend Pages
1. **Sign Up** (`/sign-up`) - Uses Appwrite for authentication
2. **Sign In** (`/sign-in`) - Uses Appwrite for authentication  
3. **Email Verification** (`/verify-email`) - Uses Appwrite
4. **User Type Selection** (`/user-type`) - Frontend only
5. **Onboarding** (`/onboarding`) - Frontend only
6. **Dashboard** (`/dashboard`) - Basic implementation

### API Endpoints Ready for Integration

#### User Management APIs
- `POST /api/users/register` - Register user in backend after Appwrite signup
- `GET /api/users/profile` - Get user profile
- `PATCH /api/users/profile` - Update user profile
- `DELETE /api/users/profile` - Delete user profile
- `POST /api/users/send-verification-email` - Send verification email
- `GET /api/users/verify-user` - Verify user email

#### Seeker APIs (Protected - requires seeker role)
- `GET /api/seeker/profile` - Get seeker profile
- `POST /api/seeker/profile` - Create seeker profile
- `PATCH /api/seeker/profile` - Update seeker profile
- `PATCH /api/seeker/onboarding/basic-info` - Update basic info (step 1)
- `PATCH /api/seeker/onboarding/location` - Update location preferences (step 2)
- `PATCH /api/seeker/onboarding/education` - Update education details (step 3)
- `PATCH /api/seeker/onboarding/skills` - Update skills (step 4)
- `PATCH /api/seeker/onboarding/resume` - Upload resume (step 5)

#### Provider APIs (Protected - requires provider role)
- `GET /api/provider/gigs` - Get all provider gigs
- `POST /api/provider/gigs` - Create new gig
- `GET /api/provider/gigs/:gigId` - Get specific gig
- `PATCH /api/provider/gigs/:gigId` - Update gig
- `DELETE /api/provider/gigs/:gigId` - Delete gig

## Authentication Flow

### Current Flow (Appwrite Only)
1. User signs up/signs in via Appwrite
2. Frontend gets JWT token from Appwrite
3. Frontend stores token in cookies
4. User navigates through frontend-only pages

### Recommended Integration Flow
1. User signs up/signs in via Appwrite
2. Frontend gets JWT token from Appwrite
3. **NEW**: Frontend calls `POST /api/users/register` to register user in backend
4. Frontend stores both Appwrite session and backend registration status
5. User selects type (provider/seeker) on `/user-type` page
6. **NEW**: Frontend calls appropriate profile creation API based on user type
7. User completes onboarding with backend API calls
8. User accesses dashboard with full backend integration

## Next Steps for Integration

### Priority 1: User Registration Integration
- Modify sign-up flow to call backend registration API after Appwrite signup
- Handle user type selection and store in backend
- Update user-type page to call backend APIs

### Priority 2: Onboarding Integration  
- Integrate seeker onboarding steps with backend APIs
- Add provider onboarding flow (currently missing in frontend)
- Update onboarding pages to save data to backend

### Priority 3: Dashboard Integration
- Connect dashboard to backend APIs
- Display user-specific data from backend
- Add profile management features

## Testing with Postman

### Import Collections
1. Import `Giggle_Backend_API_Tests.postman_collection.json`
2. Import `Giggle_Backend_Environment.postman_environment.json`
3. Update environment variables with valid JWT tokens

### Getting JWT Token for Testing
1. Sign up/sign in through frontend
2. Extract JWT token from browser cookies or network requests
3. Use token in Postman Authorization header: `Bearer <token>`

## Environment Variables

### Backend (.env)
```
PORT=5000
MONGO_URI=mongodb+srv://...
APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=67da77af003e5e94f856
APPWRITE_API_KEY=standard_...
```

### Frontend (.env.local) - Required for backend integration
```
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=67da77af003e5e94f856
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
```

## Common Issues & Solutions

### CORS Issues
- Backend has CORS configured for allowed origins
- Add frontend URL to ALLOWED_ORIGINS environment variable

### Authentication Errors
- Ensure JWT token is valid and not expired
- Check that user exists in both Appwrite and backend database
- Verify user type matches the API endpoint requirements

### Database Connection
- Ensure MongoDB connection string is correct
- Check that database indexes are created successfully
