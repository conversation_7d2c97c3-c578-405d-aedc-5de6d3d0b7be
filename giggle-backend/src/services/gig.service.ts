import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { Gig, CreateGigDto, UpdateGigDto } from "../models/gig.model";

const gigCollection = (): Collection<Gig> => db.collection<Gig>("gigs");

export const createGig = async (gigData: CreateGigDto): Promise<Gig> => {
	const newGig: Gig = {
		...gigData,
		providerId: new ObjectId(gigData.providerId),
		numberOfPeopleApplied: 0,
		shopImages: gigData.shopImages || [],
		specialization: gigData.specialization || [],
		facilities: gigData.facilities || [],
		minimumGiggleGrade: gigData.minimumGiggleGrade || 0,
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await gigCollection().insertOne(newGig);
	return { ...newGig, _id: result.insertedId.toString() };
};

export const findById = async (gigId: string): Promise<Gig | null> => {
	if (!ObjectId.isValid(gigId)) {
		return null;
	}
	return await gigCollection().findOne({
		_id: new ObjectId(gigId),
		isDeleted: false,
	});
};

export const updateGig = async (
	gigId: string,
	updateData: UpdateGigDto
): Promise<Gig | null> => {
	if (!ObjectId.isValid(gigId)) {
		return null;
	}

	const updatedGig: any = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await gigCollection().updateOne(
		{ _id: new ObjectId(gigId) },
		updatedGig
	);

	return await findById(gigId);
};

export const deleteGig = async (gigId: string): Promise<boolean> => {
	if (!ObjectId.isValid(gigId)) {
		return false;
	}

	const result = await gigCollection().updateOne(
		{ _id: new ObjectId(gigId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getAllGigs = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ gigs: Gig[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, isActive: true, ...filters };

	const gigs = await gigCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ createdAt: -1 })
		.toArray();

	const total = await gigCollection().countDocuments(query);

	return { gigs, total };
};

export const findGigsByProvider = async (providerId: string): Promise<Gig[]> => {
	if (!ObjectId.isValid(providerId)) {
		return [];
	}

	return await gigCollection()
		.find({
			providerId: new ObjectId(providerId),
			isDeleted: false,
		})
		.sort({ createdAt: -1 })
		.toArray();
};

export const findGigsByLocation = async (location: string): Promise<Gig[]> => {
	return await gigCollection()
		.find({
			isDeleted: false,
			isActive: true,
			$or: [
				{ "location.city": { $regex: location, $options: "i" } },
				{ "location.state": { $regex: location, $options: "i" } },
				{ "location.country": { $regex: location, $options: "i" } },
			],
		})
		.sort({ createdAt: -1 })
		.toArray();
};

export const findGigsByJobType = async (jobType: string): Promise<Gig[]> => {
	return await gigCollection()
		.find({
			isDeleted: false,
			isActive: true,
			jobType: jobType as any,
		})
		.sort({ createdAt: -1 })
		.toArray();
};

export const findGigsBySalaryRange = async (
	minSalary: number,
	maxSalary: number,
	currency: string = "INR"
): Promise<Gig[]> => {
	return await gigCollection()
		.find({
			isDeleted: false,
			isActive: true,
			"salary.currency": currency,
			"salary.min": { $gte: minSalary },
			"salary.max": { $lte: maxSalary },
		})
		.sort({ createdAt: -1 })
		.toArray();
};

export const incrementApplicationCount = async (gigId: string): Promise<boolean> => {
	if (!ObjectId.isValid(gigId)) {
		return false;
	}

	const result = await gigCollection().updateOne(
		{ _id: new ObjectId(gigId) },
		{
			$inc: { numberOfPeopleApplied: 1 },
			$set: { updatedAt: new Date() },
		}
	);

	return result.modifiedCount > 0;
};

export const decrementApplicationCount = async (gigId: string): Promise<boolean> => {
	if (!ObjectId.isValid(gigId)) {
		return false;
	}

	const result = await gigCollection().updateOne(
		{ _id: new ObjectId(gigId) },
		{
			$inc: { numberOfPeopleApplied: -1 },
			$set: { updatedAt: new Date() },
		}
	);

	return result.modifiedCount > 0;
};

export const searchGigs = async (
	searchTerm: string,
	page: number = 1,
	limit: number = 10
): Promise<{ gigs: Gig[]; total: number }> => {
	const skip = (page - 1) * limit;
	const searchRegex = new RegExp(searchTerm, "i");

	const query: any = {
		isDeleted: false,
		isActive: true,
		$or: [
			{ title: searchRegex },
			{ description: searchRegex },
			{ companyName: searchRegex },
			{ positionOffered: searchRegex },
			{ specialization: { $in: [searchTerm] } },
		],
	};

	const gigs = await gigCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ createdAt: -1 })
		.toArray();

	const total = await gigCollection().countDocuments(query);

	return { gigs, total };
};

export const findActiveGigs = async (): Promise<Gig[]> => {
	return await gigCollection()
		.find({
			isDeleted: false,
			isActive: true,
		})
		.sort({ createdAt: -1 })
		.toArray();
};
