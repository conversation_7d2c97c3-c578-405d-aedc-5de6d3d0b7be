import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { Company, CreateCompanyDto, UpdateCompanyDto } from "../models/company.model";

const companyCollection = (): Collection<Company> => db.collection<Company>("companies");

export const createCompany = async (companyData: CreateCompanyDto): Promise<Company> => {
	// Check if company with this name and providerId already exists
	const existingCompany = await findByNameAndProvider(
		companyData.companyName,
		companyData.providerId
	);
	if (existingCompany) {
		throw new Error("Company with this name already exists for this provider");
	}

	const newCompany: Company = {
		...companyData,
		providerId: new ObjectId(companyData.providerId),
		benefits: companyData.benefits || [],
		isVerified: false,
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await companyCollection().insertOne(newCompany);
	return { ...newCompany, _id: result.insertedId.toString() };
};

export const findById = async (companyId: string): Promise<Company | null> => {
	if (!ObjectId.isValid(companyId)) {
		return null;
	}
	return await companyCollection().findOne({
		_id: new ObjectId(companyId),
		isDeleted: false,
	});
};

export const findByNameAndProvider = async (
	companyName: string,
	providerId: string
): Promise<Company | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}

	return await companyCollection().findOne({
		companyName,
		providerId: new ObjectId(providerId),
		isDeleted: false,
	});
};

export const updateCompany = async (
	companyId: string,
	updateData: UpdateCompanyDto
): Promise<Company | null> => {
	if (!ObjectId.isValid(companyId)) {
		return null;
	}

	const updatedCompany = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await companyCollection().updateOne(
		{ _id: new ObjectId(companyId) },
		updatedCompany
	);

	return await findById(companyId);
};

export const deleteCompany = async (companyId: string): Promise<boolean> => {
	if (!ObjectId.isValid(companyId)) {
		return false;
	}

	const result = await companyCollection().updateOne(
		{ _id: new ObjectId(companyId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getAllCompanies = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ companies: Company[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, ...filters };

	const companies = await companyCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ createdAt: -1 })
		.toArray();

	const total = await companyCollection().countDocuments(query);

	return { companies, total };
};

export const findCompaniesByProvider = async (providerId: string): Promise<Company[]> => {
	if (!ObjectId.isValid(providerId)) {
		return [];
	}

	return await companyCollection()
		.find({
			providerId: new ObjectId(providerId),
			isDeleted: false,
		})
		.sort({ createdAt: -1 })
		.toArray();
};

export const findCompaniesByIndustry = async (industry: string): Promise<Company[]> => {
	return await companyCollection()
		.find({
			isDeleted: false,
			isActive: true,
			industry: industry as any,
		})
		.sort({ companyName: 1 })
		.toArray();
};

export const findCompaniesBySize = async (companySize: string): Promise<Company[]> => {
	return await companyCollection()
		.find({
			isDeleted: false,
			isActive: true,
			companySize: companySize as any,
		})
		.sort({ companyName: 1 })
		.toArray();
};

export const searchCompanies = async (
	searchTerm: string,
	page: number = 1,
	limit: number = 10
): Promise<{ companies: Company[]; total: number }> => {
	const skip = (page - 1) * limit;
	const searchRegex = { $regex: searchTerm, $options: "i" };

	const query = {
		isDeleted: false,
		isActive: true,
		$or: [
			{ companyName: searchRegex },
			{ description: searchRegex },
			{ industry: searchRegex },
		],
	};

	const companies = await companyCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ companyName: 1 })
		.toArray();

	const total = await companyCollection().countDocuments(query);

	return { companies, total };
};

export const verifyCompany = async (
	companyId: string,
	isVerified: boolean = true
): Promise<Company | null> => {
	if (!ObjectId.isValid(companyId)) {
		return null;
	}

	await companyCollection().updateOne(
		{ _id: new ObjectId(companyId) },
		{
			$set: {
				isVerified,
				updatedAt: new Date(),
			},
		}
	);

	return await findById(companyId);
};

export const findVerifiedCompanies = async (): Promise<Company[]> => {
	return await companyCollection()
		.find({
			isDeleted: false,
			isActive: true,
			isVerified: true,
		})
		.sort({ companyName: 1 })
		.toArray();
};

export const findActiveCompanies = async (): Promise<Company[]> => {
	return await companyCollection()
		.find({
			isDeleted: false,
			isActive: true,
		})
		.sort({ companyName: 1 })
		.toArray();
};
