/**
 * Utility functions for object manipulation
 */

/**
 * Removes undefined values from an object
 * @param obj - The object to clean
 * @returns The object with undefined values removed
 */
export function removeUndefinedValues<T extends Record<string, any>>(obj: T): Partial<T> {
	const cleaned = { ...obj };
	Object.keys(cleaned).forEach(key => {
		if (cleaned[key] === undefined) {
			delete cleaned[key];
		}
	});
	return cleaned;
}

/**
 * Removes null and undefined values from an object
 * @param obj - The object to clean
 * @returns The object with null and undefined values removed
 */
export function removeNullishValues<T extends Record<string, any>>(obj: T): Partial<T> {
	const cleaned = { ...obj };
	Object.keys(cleaned).forEach(key => {
		if (cleaned[key] === undefined || cleaned[key] === null) {
			delete cleaned[key];
		}
	});
	return cleaned;
}

/**
 * Removes empty values (undefined, null, empty string, empty array) from an object
 * @param obj - The object to clean
 * @returns The object with empty values removed
 */
export function removeEmptyValues<T extends Record<string, any>>(obj: T): Partial<T> {
	const cleaned = { ...obj };
	Object.keys(cleaned).forEach(key => {
		const value = cleaned[key];
		if (
			value === undefined ||
			value === null ||
			value === "" ||
			(Array.isArray(value) && value.length === 0)
		) {
			delete cleaned[key];
		}
	});
	return cleaned;
}

/**
 * Parses facilities input - handles both arrays and comma-separated strings
 * @param facilities - The facilities input (array or comma-separated string)
 * @returns Array of trimmed facility strings
 */
export function parseFacilities(facilities: string[] | string | undefined): string[] {
	if (!facilities) {
		return [];
	}

	if (Array.isArray(facilities)) {
		return facilities.map((f: string) => f.trim()).filter(f => f.length > 0);
	}

	if (typeof facilities === 'string') {
		return facilities.split(',').map((f: string) => f.trim()).filter(f => f.length > 0);
	}

	return [];
}
