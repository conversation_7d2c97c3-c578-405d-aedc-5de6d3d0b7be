import express from "express";
import {
	createSeekerProfile,
	updateSeekerProfile,
	getSeekerProfile,
	updateBasicInfo,
	updateLocationPreferences,
	updateEducationDetails,
	updateSkills,
	uploadResume,
} from "../controllers/seeker.controller";

const router = express.Router();

// Seeker onboarding and profile management routes
// Note: All routes are already protected by authenticateUser and seekerAuthMiddleware in index.ts

// Get seeker profile
router.get("/profile", getSeekerProfile);

// Create initial seeker profile
router.post("/profile", createSeekerProfile);

// Update complete seeker profile
router.patch("/profile", updateSeekerProfile);

// Onboarding step-by-step routes
// Step 1: Basic information (name, date of birth, gender, phone)
router.patch("/onboarding/basic-info", updateBasicInfo);

// Step 2: Location preferences and current location
router.patch("/onboarding/location", updateLocationPreferences);

// Step 3: Education details
router.patch("/onboarding/education", updateEducationDetails);

// Step 4: Skills
router.patch("/onboarding/skills", updateSkills);

// Step 5: Resume upload
router.patch("/onboarding/resume", uploadResume);

export default router;
