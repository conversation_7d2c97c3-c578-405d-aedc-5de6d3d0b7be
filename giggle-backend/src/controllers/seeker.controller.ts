import { Request, Response } from "express";
import {
	CreateSeekerDto,
	UpdateSeekerDto,
	BasicInfoDto,
	LocationPreferencesDto,
	EducationDetailsDto,
	SkillsDto,
	ResumeUploadDto
} from "../models/seeker.model";
import * as seekerService from "../services/seeker.service";
import { DATE_CONSTANTS } from "../utils/constants";
import { removeUndefinedValues } from "../utils/object-utils";

// Create seeker profile during onboarding
export const createSeekerProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const seekerData: CreateSeekerDto = {
			appwriteId: user.id,
			email: user.email,
			name: req.body.name,
			phoneNumber: req.body.phoneNumber,
			dateOfBirth: req.body.dateOfBirth ? new Date(req.body.dateOfBirth) : undefined,
			gender: req.body.gender,
			resumeLink: req.body.resumeLink,
			resumeFileId: req.body.resumeFileId,
			educationLevel: req.body.educationLevel || "high_school",
			employmentStatus: req.body.employmentStatus || "unemployed",
			education: req.body.education || [],
			workExperience: req.body.workExperience || [],
			skills: req.body.skills || [],
			languages: req.body.languages || [],
			certifications: req.body.certifications || [],
			portfolioLinks: req.body.portfolioLinks || [],
			expectedSalary: req.body.expectedSalary,
			availability: req.body.availability || {},
			preferences: req.body.preferences || {},
		};

		// Check if seeker profile already exists
		const existingSeeker = await seekerService.findByAppwriteId(user.id);
		if (existingSeeker) {
			res.status(409).json({ error: "Seeker profile already exists" });
			return;
		}

		const newSeeker = await seekerService.createSeeker(seekerData);
		res.status(201).json({
			success: true,
			message: "Seeker profile created successfully",
			data: newSeeker,
		});
	} catch (error: any) {
		console.error("Error creating seeker profile:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to create seeker profile" 
		});
	}
};

// Update seeker profile step by step during onboarding
export const updateSeekerProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const updateData: UpdateSeekerDto = req.body;

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			updateData
		);

		if (!updatedSeeker) {
			res.status(404).json({ 
				success: false,
				error: "Seeker profile not found" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Seeker profile updated successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error updating seeker profile:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to update seeker profile" 
		});
	}
};

// Get seeker profile
export const getSeekerProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const seeker = await seekerService.findByAppwriteId(user.id);

		if (!seeker) {
			res.status(404).json({ 
				success: false,
				error: "Seeker profile not found" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			data: seeker,
		});
	} catch (error: any) {
		console.error("Error fetching seeker profile:", error);
		res.status(500).json({ 
			success: false,
			error: "Failed to fetch seeker profile" 
		});
	}
};

// Update basic info (name, date of birth, gender, phone)
export const updateBasicInfo = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const { name, dateOfBirth, gender, phoneNumber } = req.body;

		const updateData: UpdateSeekerDto = {
			name,
			dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
			gender,
			phoneNumber,
		};

		// Remove undefined values
		const cleanedUpdateData = removeUndefinedValues(updateData);

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			cleanedUpdateData
		);

		if (!updatedSeeker) {
			res.status(404).json({ 
				success: false,
				error: "Seeker profile not found" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Basic information updated successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error updating basic info:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to update basic information" 
		});
	}
};

// Update location preferences
export const updateLocationPreferences = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const { preferences } = req.body;

		const updateData: UpdateSeekerDto = {
			preferences: {
				...preferences,
			},
		};

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			updateData
		);

		if (!updatedSeeker) {
			res.status(404).json({
				success: false,
				error: "Seeker profile not found"
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Location preferences updated successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error updating location preferences:", error);
		res.status(500).json({
			success: false,
			error: error.message || "Failed to update location preferences"
		});
	}
};

// Update education details
export const updateEducationDetails = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const { educationLevel, education, degree, specialization, completionYear, university } = req.body;

		const updateData: UpdateSeekerDto = {
			educationLevel,
			education: education || [],
		};

		// If individual education fields are provided, create an education entry
		if (degree || specialization || completionYear || university) {
			const educationEntry = {
				institution: university || "",
				degree: degree || "",
				fieldOfStudy: specialization || "",
				startDate: startYear
					? new Date(
						startYear,
						typeof startMonth === "number" ? startMonth : 8, // Default to September (month 8, zero-based)
						1
					)
					: undefined,
				endDate: completionYear ? new Date(completionYear, DATE_CONSTANTS.DEFAULT_GRADUATION_MONTH, 1) : undefined, // June 1st of completion year
				isCurrentlyStudying: !completionYear,
				grade: "",
				description: "",
			};

			updateData.education = [educationEntry];
		}

		// Remove undefined values
		const cleanedUpdateData = removeUndefinedValues(updateData);

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			cleanedUpdateData
		);

		if (!updatedSeeker) {
			res.status(404).json({
				success: false,
				error: "Seeker profile not found"
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Education details updated successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error updating education details:", error);
		res.status(500).json({
			success: false,
			error: error.message || "Failed to update education details"
		});
	}
};

// Update skills
export const updateSkills = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const { skills } = req.body;

		const updateData: UpdateSeekerDto = {
			skills: skills || [],
		};

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			updateData
		);

		if (!updatedSeeker) {
			res.status(404).json({
				success: false,
				error: "Seeker profile not found"
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Skills updated successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error updating skills:", error);
		res.status(500).json({
			success: false,
			error: error.message || "Failed to update skills"
		});
	}
};

// Upload resume
export const uploadResume = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const { resumeLink, resumeFileId } = req.body;

		const updateData: UpdateSeekerDto = {
			resumeLink,
			resumeFileId,
		};

		// Remove undefined values
		const cleanedUpdateData = removeUndefinedValues(updateData);

		const updatedSeeker = await seekerService.updateSeekerByAppwriteId(
			user.id,
			cleanedUpdateData
		);

		if (!updatedSeeker) {
			res.status(404).json({
				success: false,
				error: "Seeker profile not found"
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Resume uploaded successfully",
			data: updatedSeeker,
		});
	} catch (error: any) {
		console.error("Error uploading resume:", error);
		res.status(500).json({
			success: false,
			error: error.message || "Failed to upload resume"
		});
	}
};
