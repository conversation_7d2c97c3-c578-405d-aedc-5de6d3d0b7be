import { Request, Response } from "express";
import { CreateGigDto, UpdateGigDto } from "../models/gig.model";
import * as gigService from "../services/gig.service";
import * as providerService from "../services/provider.service";
import { GRADE_MAPPINGS, LOCATION_DEFAULTS } from "../utils/constants";
import { removeUndefinedValues, parseFacilities } from "../utils/object-utils";

// Create a new gig (List Your Gig)
export const createGig = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		
		// Get provider details
		const provider = await providerService.findByAppwriteId(user.id);
		if (!provider) {
			res.status(404).json({ 
				success: false,
				error: "Provider profile not found" 
			});
			return;
		}

		const gigData: CreateGigDto = {
			title: req.body.jobRole || "",
			description: req.body.otherRequirements || "",
			salary: {
				min: isNaN(parseInt(req.body.salary, 10)) ? 0 : parseInt(req.body.salary, 10),
				max: isNaN(parseInt(req.body.salary, 10)) ? 0 : parseInt(req.body.salary, 10),
				currency: "INR",
				period: "monthly"
			},
			numberOfOpenPositions: 1,
			location: req.body.latitude && req.body.longitude && req.body.address ? {
				latitude: parseFloat(req.body.latitude),
				longitude: parseFloat(req.body.longitude),
				address: req.body.address.trim()
			} : {
				latitude: LOCATION_DEFAULTS.LATITUDE,
				longitude: LOCATION_DEFAULTS.LONGITUDE,
				address: LOCATION_DEFAULTS.ADDRESS
			},
			jobType: req.body.jobType || "full-time",
			jobTrait: req.body.remoteWork ? "remote" : "on-site",
			providerId: provider._id?.toString() || "",
			companyName: req.body.companyName || "",
			positionOffered: req.body.jobRole || "",
			minimumQualificationRequired: "",
			experience: {
				min: 0,
				max: 5,
				description: ""
			},
			specialization: [],
			facilities: parseFacilities(req.body.facilities),
			minimumGiggleGrade: GRADE_MAPPINGS[req.body.requiredGiggleGrade as keyof typeof GRADE_MAPPINGS] || GRADE_MAPPINGS.DEFAULT
		};

		const newGig = await gigService.createGig(gigData);
		res.status(201).json({
			success: true,
			message: "Gig created successfully",
			data: newGig,
		});
	} catch (error: any) {
		console.error("Error creating gig:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to create gig" 
		});
	}
};

// Get provider's gigs
export const getProviderGigs = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		
		// Get provider details
		const provider = await providerService.findByAppwriteId(user.id);
		if (!provider) {
			res.status(404).json({ 
				success: false,
				error: "Provider profile not found" 
			});
			return;
		}

		const gigs = await gigService.findGigsByProvider(provider._id?.toString() || "");
		res.status(200).json({
			success: true,
			data: gigs,
		});
	} catch (error: any) {
		console.error("Error fetching provider gigs:", error);
		res.status(500).json({ 
			success: false,
			error: "Failed to fetch gigs" 
		});
	}
};

// Update a gig
export const updateGig = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const { gigId } = req.params;
		const user = req.user;
		
		// Get provider details
		const provider = await providerService.findByAppwriteId(user.id);
		if (!provider) {
			res.status(404).json({ 
				success: false,
				error: "Provider profile not found" 
			});
			return;
		}

		// Check if gig belongs to this provider
		const existingGig = await gigService.findById(gigId);
		if (!existingGig || existingGig.providerId.toString() !== provider._id?.toString()) {
			res.status(404).json({ 
				success: false,
				error: "Gig not found or access denied" 
			});
			return;
		}

		const updateData: UpdateGigDto = {
			title: req.body.jobRole,
			description: req.body.otherRequirements,
			salary: req.body.salary ? {
				min: parseInt(req.body.salary, 10),
				max: parseInt(req.body.salary, 10),
				currency: "INR",
				period: "monthly"
			} : undefined,
			jobType: req.body.jobType,
			jobTrait: req.body.remoteWork ? "remote" : "on-site",
			companyName: req.body.companyName,
			positionOffered: req.body.jobRole,
			facilities: req.body.facilities ? parseFacilities(req.body.facilities) : undefined,
			minimumGiggleGrade: req.body.requiredGiggleGrade
				? GRADE_MAPPINGS[req.body.requiredGiggleGrade as keyof typeof GRADE_MAPPINGS] || GRADE_MAPPINGS.DEFAULT
				: undefined
		};

		// Remove undefined values
		const cleanedUpdateData = removeUndefinedValues(updateData);

		const updatedGig = await gigService.updateGig(gigId, cleanedUpdateData);
		if (!updatedGig) {
			res.status(404).json({ 
				success: false,
				error: "Gig not found" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Gig updated successfully",
			data: updatedGig,
		});
	} catch (error: any) {
		console.error("Error updating gig:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to update gig" 
		});
	}
};

// Delete a gig
export const deleteGig = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const { gigId } = req.params;
		const user = req.user;
		
		// Get provider details
		const provider = await providerService.findByAppwriteId(user.id);
		if (!provider) {
			res.status(404).json({ 
				success: false,
				error: "Provider profile not found" 
			});
			return;
		}

		// Check if gig belongs to this provider
		const existingGig = await gigService.findById(gigId);
		if (!existingGig || existingGig.providerId.toString() !== provider._id?.toString()) {
			res.status(404).json({ 
				success: false,
				error: "Gig not found or access denied" 
			});
			return;
		}

		const deleted = await gigService.deleteGig(gigId);
		if (!deleted) {
			res.status(404).json({ 
				success: false,
				error: "Gig not found" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			message: "Gig deleted successfully",
		});
	} catch (error: any) {
		console.error("Error deleting gig:", error);
		res.status(500).json({ 
			success: false,
			error: error.message || "Failed to delete gig" 
		});
	}
};

// Get a specific gig
export const getGig = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const { gigId } = req.params;
		const user = req.user;
		
		// Get provider details
		const provider = await providerService.findByAppwriteId(user.id);
		if (!provider) {
			res.status(404).json({ 
				success: false,
				error: "Provider profile not found" 
			});
			return;
		}

		const gig = await gigService.findById(gigId);
		if (!gig || gig.providerId.toString() !== provider._id?.toString()) {
			res.status(404).json({ 
				success: false,
				error: "Gig not found or access denied" 
			});
			return;
		}

		res.status(200).json({
			success: true,
			data: gig,
		});
	} catch (error: any) {
		console.error("Error fetching gig:", error);
		res.status(500).json({ 
			success: false,
			error: "Failed to fetch gig" 
		});
	}
};
