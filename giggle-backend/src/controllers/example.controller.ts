import { Request, Response } from "express";

export const exampleController = async (req: Request, res: Response) => {
	try {
		// Example controller logic
		res.status(200).json({
			success: true,
			message: "Example endpoint working!",
			user: req.user, // This will be populated by the auth middleware
			timestamp: new Date().toISOString()
		});
	} catch (error) {
		console.error("Example controller error:", error);
		res.status(500).json({
			success: false,
			message: "Internal server error"
		});
	}
};
