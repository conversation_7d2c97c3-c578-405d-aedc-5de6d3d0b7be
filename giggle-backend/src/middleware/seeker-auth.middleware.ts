import { NextFunction, Request, Response } from "express";
import { USER_TYPES } from "../utils/constants";

export const seekerAuthMiddleware = async (
	req: Request,
	res: Response,
	next: NextFunction
): Promise<void> => {
	try {
		const user = req.user;

		if (user.type !== USER_TYPES.SEEKER) {
			res.status(403).json({ message: "Forbidden: Seeker access only" });
			return;
		}

		next();
	} catch (error) {
		console.error("Error in seekerAuthMiddleware:", error);
		res.status(500).json({ message: "Internal server error" });
	}
};
