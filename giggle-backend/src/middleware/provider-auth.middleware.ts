import { NextFunction, Request, Response } from "express";
import { USER_TYPES } from "../utils/constants";

export const providerAuthMiddleware = async (
	req: Request,
	res: Response,
	next: NextFunction
): Promise<void> => {
	try {
		const user = req.user;

		if (!user) {
			res
				.status(401)
				.json({ message: "Unauthorized: Authentication required" });
			return;
		}

		if (user.type !== USER_TYPES.PROVIDER) {
			res.status(403).json({ message: "Forbidden: Provider access only" });
			return;
		}

		next();
	} catch (error) {
		console.error("Error in providerAuthMiddleware:", error);
		res.status(500).json({ message: "Internal server error" });
	}
};
