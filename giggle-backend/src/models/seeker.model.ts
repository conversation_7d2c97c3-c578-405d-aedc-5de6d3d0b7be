import { ObjectId } from "mongodb";

export type EducationLevel = "no_formal_education" | "primary" | "secondary" | "high_school" | "diploma" | "bachelor" | "master" | "phd" | "other";
export type EmploymentStatus = "unemployed" | "employed" | "self_employed" | "student" | "retired" | "other";

export interface Education {
	institution: string;
	degree: string;
	fieldOfStudy: string;
	startDate: Date;
	endDate?: Date;
	isCurrentlyStudying: boolean;
	grade?: string;
	description?: string;
}

export interface WorkExperience {
	company: string;
	position: string;
	startDate: Date;
	endDate?: Date;
	isCurrentlyWorking: boolean;
	description?: string;
	skills?: string[];
}

export interface Skill {
	name: string;
	level: "beginner" | "intermediate" | "advanced" | "expert";
	yearsOfExperience?: number;
	certified?: boolean;
}

export interface Seeker {
	_id?: string | ObjectId;
	appwriteId: string; // Appwrite authentication ID
	email: string;
	name: string;
	phoneNumber?: string;
	phoneVerified: boolean;

	// Profile Information
	resumeLink?: string; // URL or file ID for resume
	resumeFileId?: string | ObjectId; // Reference to File
	flnScore?: number; // Reference to current FLN score
	profilePicture?: string; // URL or file ID
	bio?: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";

	// Professional Information
	educationLevel: EducationLevel;
	employmentStatus: EmploymentStatus;
	education: Education[];
	workExperience: WorkExperience[];
	skills: Skill[];
	languages: string[];
	certifications?: string[];
	portfolioLinks?: string[];

	// Job Search Preferences
	expectedSalary?: {
		min: number;
		max: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availability: {
		startDate?: Date;
		noticePeriod?: number; // in days
		workingHours?: "full-time" | "part-time" | "flexible";
	};
	preferences: {
		jobTypes?: string[];
		industries?: string[];
		locations?: string[];
		remoteWork?: boolean;
		willingToRelocate?: boolean;
	};

	// Status flags
	isActive: boolean;
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateSeekerDto {
	appwriteId: string;
	email: string;
	name: string;
	phoneNumber?: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	resumeLink?: string;
	resumeFileId?: string;
	educationLevel: EducationLevel;
	employmentStatus: EmploymentStatus;
	education?: Education[];
	workExperience?: WorkExperience[];
	skills?: Skill[];
	languages?: string[];
	certifications?: string[];
	portfolioLinks?: string[];
	expectedSalary?: {
		min: number;
		max: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availability?: {
		startDate?: Date;
		noticePeriod?: number;
		workingHours?: "full-time" | "part-time" | "flexible";
	};
	preferences?: {
		jobTypes?: string[];
		industries?: string[];
		locations?: string[];
		remoteWork?: boolean;
		willingToRelocate?: boolean;
	};
}

export interface UpdateSeekerDto {
	name?: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phoneNumber?: string;
	resumeLink?: string;
	resumeFileId?: string;
	educationLevel?: EducationLevel;
	employmentStatus?: EmploymentStatus;
	education?: Education[];
	workExperience?: WorkExperience[];
	skills?: Skill[];
	languages?: string[];
	certifications?: string[];
	portfolioLinks?: string[];
	expectedSalary?: {
		min?: number;
		max?: number;
		currency?: string;
		period?: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availability?: {
		startDate?: Date;
		noticePeriod?: number;
		workingHours?: "full-time" | "part-time" | "flexible";
	};
	preferences?: {
		jobTypes?: string[];
		industries?: string[];
		locations?: string[];
		remoteWork?: boolean;
		willingToRelocate?: boolean;
	};
	isActive?: boolean;
}

// Onboarding step DTOs
export interface BasicInfoDto {
	name: string;
	dateOfBirth?: string;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phoneNumber?: string;
}

export interface LocationPreferencesDto {
	preferences: {
		locations?: string[];
		remoteWork?: boolean;
		willingToRelocate?: boolean;
	};
}

export interface EducationDetailsDto {
	educationLevel?: EducationLevel;
	degree?: string;
	specialization?: string;
	completionYear?: string;
	university?: string;
	education?: Education[];
}

export interface SkillsDto {
	skills: Skill[];
}

export interface ResumeUploadDto {
	resumeLink?: string;
	resumeFileId?: string;
}

// Helper type for MongoDB document with ObjectId
export type SeekerDocument = Omit<Seeker, "_id"> & {
	_id: ObjectId;
};
