import { ObjectId } from "mongodb";

export type VerificationStatus = "pending" | "in_review" | "verified" | "rejected";

export interface CompanyAddress {
	city: string;
	state: string;
	country: string;
	fullOperationalAddress: string;
	pincode: number;
	companyEmail: string;
	companyPhone: number;
	website?: string;
	socialMediaLinks?: {
		linkedin?: string;
		instagram?: string;
		facebook?: string;
		twitter?: string;
	};
}

export interface ProviderBranding {
	companyLogo?: string; // URL or file ID
	businessRegistrationNumber?: string;
	gstinOrMsmeId?: string; // GSTIN / MSME ID
	uploadedDocuments?: string[]; // Array of file IDs for licenses, IDs, proof
	requestVerifiedBadge: boolean;
}

export interface ProfileCompletionSteps {
	personalAccount: boolean;
	companyAddress: boolean;
	brandingVerification: boolean;
}

export interface Provider {
	_id?: string | ObjectId;
	appwriteId: string; // Appwrite authentication ID
	email: string;
	name: string;

	// Page 1: Personal Account
	phoneNumber: string;
	phoneVerified: boolean;
	otpVerifiedAt?: Date;
	roleTitle?: string; // e.g., Franchise Owner, Recruiter
	profilePhoto?: string; // URL or file ID
	termsAccepted: boolean;
	privacyPolicyAccepted: boolean;

	// Page 2: Company Address
	companyAddress?: CompanyAddress;

	// Page 3: Branding & Verification
	branding?: ProviderBranding;

	// Verification status
	verificationStatus: VerificationStatus;
	verificationRequestedAt?: Date;
	verificationCompletedAt?: Date;
	verificationNotes?: string;
	verifiedBy?: string | ObjectId; // Admin who verified

	// Profile completion tracking
	profileCompletionSteps: ProfileCompletionSteps;
	profileCompletionPercentage: number; // 0-100

	// Status flags
	isActive: boolean;
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// DTOs for different sign-up pages
export interface CreateProviderPersonalAccountDto {
	provider_id: string;
	email: string;
	name: string;
	phoneNumber: string;
	roleTitle?: string;
	profilePhoto?: string;
	termsAccepted: boolean;
	privacyPolicyAccepted: boolean;
}

export interface UpdateProviderCompanyAddressDto {
	companyAddress: CompanyAddress;
}

export interface UpdateProviderBrandingDto {
	branding: ProviderBranding;
}

export interface CreateProviderDto {
	appwriteId: string;
	email: string;
	name: string;
	phoneNumber: string;
	roleTitle?: string;
	profilePhoto?: string;
	termsAccepted: boolean;
	privacyPolicyAccepted: boolean;
}

export interface UpdateProviderDto {
	phoneNumber?: string;
	phoneVerified?: boolean;
	roleTitle?: string;
	profilePhoto?: string;
	companyAddress?: CompanyAddress;
	branding?: ProviderBranding;
	verificationStatus?: VerificationStatus;
	verificationNotes?: string;
	verifiedBy?: string;
	isActive?: boolean;
}

export interface VerifyProviderPhoneDto {
	phoneNumber: string;
	otpCode: string;
}

export interface RequestVerificationDto {
	requestVerifiedBadge: boolean;
	additionalNotes?: string;
}

// Helper type for MongoDB document with ObjectId
export type ProviderDocument = Omit<Provider, "_id"> & {
	_id: ObjectId;
};

// Helper functions for profile completion calculation
export const calculateProfileCompletion = (provider: Provider): number => {
	const steps = provider.profileCompletionSteps;
	const totalSteps = 3;
	const completedSteps = Object.values(steps).filter(Boolean).length;
	return Math.round((completedSteps / totalSteps) * 100);
};

export const getNextRequiredStep = (provider: Provider): string | null => {
	const steps = provider.profileCompletionSteps;
	
	if (!steps.personalAccount) return "personalAccount";
	if (!steps.companyAddress) return "companyAddress";
	if (!steps.brandingVerification) return "brandingVerification";
	
	return null; // All steps completed
};

export const isProfileComplete = (provider: Provider): boolean => {
	const steps = provider.profileCompletionSteps;
	return steps.personalAccount && steps.companyAddress && steps.brandingVerification;
};
