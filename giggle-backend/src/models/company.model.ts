import { ObjectId } from "mongodb";
import { Location } from "./user.model";

export type CompanySize = "startup" | "small" | "medium" | "large" | "enterprise";
export type Industry = "technology" | "healthcare" | "finance" | "education" | "retail" | "manufacturing" | "services" | "other";

export interface Company {
	_id?: string | ObjectId;
	providerId: string | ObjectId; // Reference to Provider._id
	companyName: string;
	description?: string;
	industry: Industry;
	companySize: CompanySize;
	website?: string;
	email?: string;
	phone?: string | number; // Support both string and number formats
	location?: Location;
	logo?: string; // URL or file ID for company logo
	establishedYear?: number;
	registrationNumber?: string; // Business registration number
	taxId?: string;
	socialMedia?: {
		linkedin?: string;
		twitter?: string;
		facebook?: string;
		instagram?: string;
	};
	benefits?: string[]; // Company-wide benefits
	culture?: string; // Company culture description
	isVerified: boolean; // Whether the company is verified
	isActive: boolean;
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateCompanyDto {
	providerId: string;
	companyName: string;
	description?: string;
	industry: Industry;
	companySize: CompanySize;
	website?: string;
	email?: string;
	phone?: string | number;
	location?: Location;
	logo?: string;
	establishedYear?: number;
	registrationNumber?: string;
	taxId?: string;
	socialMedia?: {
		linkedin?: string;
		twitter?: string;
		facebook?: string;
		instagram?: string;
	};
	benefits?: string[];
	culture?: string;
}

export interface UpdateCompanyDto {
	companyName?: string;
	description?: string;
	industry?: Industry;
	companySize?: CompanySize;
	website?: string;
	email?: string;
	phone?: string | number;
	location?: Location;
	logo?: string;
	establishedYear?: number;
	registrationNumber?: string;
	taxId?: string;
	socialMedia?: {
		linkedin?: string;
		twitter?: string;
		facebook?: string;
		instagram?: string;
	};
	benefits?: string[];
	culture?: string;
	isVerified?: boolean;
	isActive?: boolean;
}

// Helper type for MongoDB document with ObjectId
export type CompanyDocument = Omit<Company, "_id"> & {
	_id: ObjectId;
};
