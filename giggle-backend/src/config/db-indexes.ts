import { db } from "./db";

export async function createDatabaseIndexes() {
	try {
		console.log("Creating database indexes...");

		// Users collection indexes
		await db.collection("users").createIndex({ appwriteId: 1 }, { unique: true });
		await db.collection("users").createIndex({ email: 1 });
		await db.collection("users").createIndex({ userType: 1 });
		await db.collection("users").createIndex({ isDeleted: 1 });
		await db.collection("users").createIndex({ createdAt: -1 });

		// Providers collection indexes
		await db.collection("providers").createIndex({ appwriteId: 1 }, { unique: true });
		await db.collection("providers").createIndex({ email: 1 });
		await db.collection("providers").createIndex({ verificationStatus: 1 });
		await db.collection("providers").createIndex({ isDeleted: 1 });
		await db.collection("providers").createIndex({ phoneVerified: 1 });
		await db.collection("providers").createIndex({ createdAt: -1 });

		// Seekers collection indexes
		await db.collection("seekers").createIndex({ appwriteId: 1 }, { unique: true });
		await db.collection("seekers").createIndex({ email: 1 });
		await db.collection("seekers").createIndex({ isDeleted: 1 });
		await db.collection("seekers").createIndex({ isActive: 1 });
		await db.collection("seekers").createIndex({ educationLevel: 1 });
		await db.collection("seekers").createIndex({ employmentStatus: 1 });
		await db.collection("seekers").createIndex({ "skills.name": 1 });
		await db.collection("seekers").createIndex({ flnScore: -1 });
		await db.collection("seekers").createIndex({ createdAt: -1 });

		// Gigs collection indexes
		await db.collection("gigs").createIndex({ providerId: 1 });
		await db.collection("gigs").createIndex({ isDeleted: 1 });
		await db.collection("gigs").createIndex({ isActive: 1 });
		await db.collection("gigs").createIndex({ jobType: 1 });
		await db.collection("gigs").createIndex({ jobTrait: 1 });
		await db.collection("gigs").createIndex({ "location.city": 1 });
		await db.collection("gigs").createIndex({ "location.state": 1 });
		await db.collection("gigs").createIndex({ "location.country": 1 });
		await db.collection("gigs").createIndex({ "salary.min": 1, "salary.max": 1 });
		await db.collection("gigs").createIndex({ "salary.currency": 1 });
		await db.collection("gigs").createIndex({ minimumGiggleGrade: 1 });
		await db.collection("gigs").createIndex({ specialization: 1 });
		await db.collection("gigs").createIndex({ createdAt: -1 });
		await db.collection("gigs").createIndex({ updatedAt: -1 });

		// Companies collection indexes
		await db.collection("companies").createIndex({ providerId: 1 });
		await db.collection("companies").createIndex({ companyName: 1 });
		await db.collection("companies").createIndex({ industry: 1 });
		await db.collection("companies").createIndex({ companySize: 1 });
		await db.collection("companies").createIndex({ isDeleted: 1 });
		await db.collection("companies").createIndex({ isActive: 1 });
		await db.collection("companies").createIndex({ isVerified: 1 });
		await db.collection("companies").createIndex({ createdAt: -1 });

		// FLN scores collection indexes
		await db.collection("fln_scores").createIndex({ seekerId: 1 }, { unique: true });
		await db.collection("fln_scores").createIndex({ flnScore: -1 });
		await db.collection("fln_scores").createIndex({ isDeleted: 1 });
		await db.collection("fln_scores").createIndex({ "fluencyRecord.testDate": -1 });
		await db.collection("fln_scores").createIndex({ createdAt: -1 });

		// Gig applications collection indexes
		await db.collection("gig_applications").createIndex({ seekerId: 1 });
		await db.collection("gig_applications").createIndex({ gigId: 1 });
		await db.collection("gig_applications").createIndex({ seekerId: 1, gigId: 1 }, { unique: true });
		await db.collection("gig_applications").createIndex({ status: 1 });
		await db.collection("gig_applications").createIndex({ isDeleted: 1 });
		await db.collection("gig_applications").createIndex({ appliedAt: -1 });
		await db.collection("gig_applications").createIndex({ reviewedAt: -1 });
		await db.collection("gig_applications").createIndex({ reviewedBy: 1 });

		// Files collection indexes
		await db.collection("files").createIndex({ fileId: 1 }, { unique: true });
		await db.collection("files").createIndex({ uploadedBy: 1 });
		await db.collection("files").createIndex({ uploaderType: 1 });
		await db.collection("files").createIndex({ fileType: 1 });
		await db.collection("files").createIndex({ mimeType: 1 });
		await db.collection("files").createIndex({ active: 1 });
		await db.collection("files").createIndex({ isDeleted: 1 });
		await db.collection("files").createIndex({ uploadedAt: -1 });

		// Compound indexes for better query performance
		await db.collection("gigs").createIndex({ 
			isDeleted: 1, 
			isActive: 1, 
			createdAt: -1 
		});

		await db.collection("gig_applications").createIndex({ 
			isDeleted: 1, 
			status: 1, 
			appliedAt: -1 
		});

		await db.collection("seekers").createIndex({ 
			isDeleted: 1, 
			isActive: 1, 
			flnScore: -1 
		});

		await db.collection("providers").createIndex({ 
			isDeleted: 1, 
			verificationStatus: 1, 
			createdAt: -1 
		});

		await db.collection("files").createIndex({ 
			isDeleted: 1, 
			active: 1, 
			uploadedBy: 1 
		});

		console.log("Database indexes created successfully");
	} catch (error) {
		console.error("Error creating database indexes:", error);
		// Don't throw error as indexes might already exist
	}
}
