"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seekerAuthMiddleware = void 0;
const constants_1 = require("../utils/constants");
const seekerAuthMiddleware = async (req, res, next) => {
    try {
        const user = req.user;
        if (user.type !== constants_1.USER_TYPES.SEEKER) {
            res.status(403).json({ message: "Forbidden: Seeker access only" });
            return;
        }
        next();
    }
    catch (error) {
        console.error("Error in seekerAuthMiddleware:", error);
        res.status(500).json({ message: "Internal server error" });
    }
};
exports.seekerAuthMiddleware = seekerAuthMiddleware;
