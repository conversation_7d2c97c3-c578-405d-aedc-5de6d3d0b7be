"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
exports.connectToDB = connectToDB;
exports.closeDBConnection = closeDBConnection;
const mongodb_1 = require("mongodb");
const dotenv_1 = __importDefault(require("dotenv"));
const db_indexes_1 = require("./db-indexes");
dotenv_1.default.config();
const uri = process.env.MONGO_URI || "mongodb://localhost:27017";
const client = new mongodb_1.MongoClient(uri);
async function connectToDB() {
    try {
        await client.connect();
        exports.db = client.db("giggle");
        console.log("Connected to MongoDB");
        // Create database indexes for better performance
        await (0, db_indexes_1.createDatabaseIndexes)();
    }
    catch (err) {
        console.error("DB connection failed", err);
        process.exit(1);
    }
}
async function closeDBConnection() {
    try {
        await client.close();
        console.log("Disconnected from MongoDB");
    }
    catch (err) {
        console.error("Error closing DB connection", err);
    }
}
