"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = exports.updateUser = exports.findById = exports.findByAppwriteId = exports.createUser = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const userCollection = () => db_1.db.collection("users");
const createUser = async (userData) => {
    // Check if user with this appwriteId already exists
    const existingUser = await (0, exports.findByAppwriteId)(userData.appwriteId);
    if (existingUser) {
        throw new Error("User already exists");
    }
    const newUser = {
        ...userData,
        userType: userData.userType,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
    };
    const result = await userCollection().insertOne(newUser);
    return { ...newUser, _id: result.insertedId.toString() };
};
exports.createUser = createUser;
const findByAppwriteId = async (appwriteId) => {
    return await userCollection().findOne({
        appwriteId,
        isDeleted: false,
    });
};
exports.findByAppwriteId = findByAppwriteId;
const findById = async (userId) => {
    if (!mongodb_1.ObjectId.isValid(userId)) {
        return null;
    }
    return await userCollection().findOne({
        _id: new mongodb_1.ObjectId(userId),
        isDeleted: false,
    });
};
exports.findById = findById;
const updateUser = async (appwriteId, updateData) => {
    const user = await (0, exports.findByAppwriteId)(appwriteId);
    if (!user) {
        return null;
    }
    const updatedUser = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await userCollection().updateOne({ appwriteId }, updatedUser);
    return await (0, exports.findByAppwriteId)(appwriteId);
};
exports.updateUser = updateUser;
const deleteUser = async (appwriteId) => {
    const user = await (0, exports.findByAppwriteId)(appwriteId);
    if (!user) {
        return false;
    }
    const result = await userCollection().updateOne({ appwriteId }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteUser = deleteUser;
