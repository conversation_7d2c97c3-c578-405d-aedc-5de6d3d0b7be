"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLatestFlnRecord = exports.findSeekersByFlnRange = exports.getFlnStatistics = exports.deleteFlnRecord = exports.getFlnHistory = exports.updateFlnScore = exports.findById = exports.findBySeekerIdActive = exports.createFlnRecord = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const flnCollection = () => db_1.db.collection("fln_scores");
const createFlnRecord = async (seekerId, flnScore, details) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        throw new Error("Invalid seeker ID");
    }
    // Check if FLN record already exists for this seeker
    const existingFln = await (0, exports.findBySeekerIdActive)(seekerId);
    const newFlnRecord = {
        score: flnScore,
        details,
        testDate: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Valid for 1 year
    };
    if (existingFln) {
        // Update existing record
        const updatedFln = await flnCollection().updateOne({ _id: new mongodb_1.ObjectId(existingFln._id) }, {
            $set: {
                flnScore,
                details,
                updatedAt: new Date(),
            },
            $push: {
                fluencyRecord: newFlnRecord,
            },
        });
        return await (0, exports.findBySeekerIdActive)(seekerId);
    }
    else {
        // Create new FLN record
        const newFln = {
            seekerId: new mongodb_1.ObjectId(seekerId),
            flnScore,
            details,
            fluencyRecord: [newFlnRecord],
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const result = await flnCollection().insertOne(newFln);
        return { ...newFln, _id: result.insertedId.toString() };
    }
};
exports.createFlnRecord = createFlnRecord;
const findBySeekerIdActive = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    return await flnCollection().findOne({
        seekerId: new mongodb_1.ObjectId(seekerId),
        isDeleted: false,
    });
};
exports.findBySeekerIdActive = findBySeekerIdActive;
const findById = async (flnId) => {
    if (!mongodb_1.ObjectId.isValid(flnId)) {
        return null;
    }
    return await flnCollection().findOne({
        _id: new mongodb_1.ObjectId(flnId),
        isDeleted: false,
    });
};
exports.findById = findById;
const updateFlnScore = async (seekerId, flnScore, details) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    const newFlnRecord = {
        score: flnScore,
        details,
        testDate: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Valid for 1 year
    };
    await flnCollection().updateOne({ seekerId: new mongodb_1.ObjectId(seekerId) }, {
        $set: {
            flnScore,
            details,
            updatedAt: new Date(),
        },
        $push: {
            fluencyRecord: newFlnRecord,
        },
    });
    return await (0, exports.findBySeekerIdActive)(seekerId);
};
exports.updateFlnScore = updateFlnScore;
const getFlnHistory = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return [];
    }
    const flnRecord = await flnCollection().findOne({
        seekerId: new mongodb_1.ObjectId(seekerId),
        isDeleted: false,
    });
    return flnRecord?.fluencyRecord || [];
};
exports.getFlnHistory = getFlnHistory;
const deleteFlnRecord = async (flnId) => {
    if (!mongodb_1.ObjectId.isValid(flnId)) {
        return false;
    }
    const result = await flnCollection().updateOne({ _id: new mongodb_1.ObjectId(flnId) }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteFlnRecord = deleteFlnRecord;
const getFlnStatistics = async () => {
    const pipeline = [
        { $match: { isDeleted: false } },
        {
            $group: {
                _id: null,
                averageScore: { $avg: "$flnScore" },
                totalRecords: { $sum: 1 },
                scores: { $push: "$flnScore" },
            },
        },
    ];
    const result = await flnCollection().aggregate(pipeline).toArray();
    if (result.length === 0) {
        return {
            averageScore: 0,
            totalRecords: 0,
            scoreDistribution: [],
        };
    }
    const { averageScore, totalRecords, scores } = result[0];
    // Calculate score distribution
    const scoreDistribution = [
        { range: "0-20", count: scores.filter((s) => s >= 0 && s < 20).length },
        { range: "20-40", count: scores.filter((s) => s >= 20 && s < 40).length },
        { range: "40-60", count: scores.filter((s) => s >= 40 && s < 60).length },
        { range: "60-80", count: scores.filter((s) => s >= 60 && s < 80).length },
        { range: "80-100", count: scores.filter((s) => s >= 80 && s <= 100).length },
    ];
    return {
        averageScore: Math.round(averageScore * 100) / 100,
        totalRecords,
        scoreDistribution,
    };
};
exports.getFlnStatistics = getFlnStatistics;
const findSeekersByFlnRange = async (minScore, maxScore) => {
    return await flnCollection()
        .find({
        isDeleted: false,
        flnScore: { $gte: minScore, $lte: maxScore },
    })
        .sort({ flnScore: -1 })
        .toArray();
};
exports.findSeekersByFlnRange = findSeekersByFlnRange;
const getLatestFlnRecord = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return null;
    }
    const flnRecord = await flnCollection().findOne({
        seekerId: new mongodb_1.ObjectId(seekerId),
        isDeleted: false,
    });
    if (!flnRecord || !flnRecord.fluencyRecord || flnRecord.fluencyRecord.length === 0) {
        return null;
    }
    // Return the most recent record
    return flnRecord.fluencyRecord.sort((a, b) => new Date(b.testDate).getTime() - new Date(a.testDate).getTime())[0];
};
exports.getLatestFlnRecord = getLatestFlnRecord;
