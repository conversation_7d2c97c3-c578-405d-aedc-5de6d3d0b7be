"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findVerifiedProviders = exports.updateVerificationStatus = exports.getAllProviders = exports.deleteProvider = exports.updateProviderByAppwriteId = exports.updateProvider = exports.findByAppwriteId = exports.findById = exports.createProvider = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const providerCollection = () => db_1.db.collection("providers");
const createProvider = async (providerData) => {
    // Check if provider with this appwriteId already exists
    const existingProvider = await (0, exports.findByAppwriteId)(providerData.appwriteId);
    if (existingProvider) {
        throw new Error("Provider with this Appwrite ID already exists");
    }
    const newProvider = {
        ...providerData,
        phoneVerified: false,
        verificationStatus: "pending",
        profileCompletionSteps: {
            personalAccount: true,
            companyAddress: false,
            brandingVerification: false
        },
        profileCompletionPercentage: 33, // Personal account completed
        isActive: true,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await providerCollection().insertOne(newProvider);
    return { ...newProvider, _id: result.insertedId.toString() };
};
exports.createProvider = createProvider;
const findById = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    return await providerCollection().findOne({
        _id: new mongodb_1.ObjectId(providerId),
        isDeleted: false,
    });
};
exports.findById = findById;
const findByAppwriteId = async (appwriteId) => {
    return await providerCollection().findOne({
        appwriteId,
        isDeleted: false,
    });
};
exports.findByAppwriteId = findByAppwriteId;
const updateProvider = async (providerId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    const updatedProvider = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId), isDeleted: false }, updatedProvider);
    return await (0, exports.findById)(providerId);
};
exports.updateProvider = updateProvider;
const updateProviderByAppwriteId = async (appwriteId, updateData) => {
    const updatedProvider = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await providerCollection().updateOne({ appwriteId, isDeleted: false }, updatedProvider);
    return await (0, exports.findByAppwriteId)(appwriteId);
};
exports.updateProviderByAppwriteId = updateProviderByAppwriteId;
const deleteProvider = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return false;
    }
    const result = await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId), isDeleted: false }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteProvider = deleteProvider;
const getAllProviders = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, ...filters };
    const providers = await providerCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .toArray();
    const total = await providerCollection().countDocuments(query);
    return { providers, total };
};
exports.getAllProviders = getAllProviders;
const updateVerificationStatus = async (providerId, status, notes, verifiedBy) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    const updateData = {
        verificationStatus: status,
        updatedAt: new Date(),
    };
    if (status === "verified") {
        updateData.verificationCompletedAt = new Date();
    }
    if (notes) {
        updateData.verificationNotes = notes;
    }
    if (verifiedBy) {
        updateData.verifiedBy = new mongodb_1.ObjectId(verifiedBy);
    }
    await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId), isDeleted: false }, { $set: updateData });
    return await (0, exports.findById)(providerId);
};
exports.updateVerificationStatus = updateVerificationStatus;
const findVerifiedProviders = async () => {
    return await providerCollection()
        .find({
        isDeleted: false,
        verificationStatus: "verified",
    })
        .toArray();
};
exports.findVerifiedProviders = findVerifiedProviders;
