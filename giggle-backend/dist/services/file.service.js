"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchFiles = exports.getFileStatistics = exports.findFilesByMimeType = exports.activateFile = exports.deactivateFile = exports.findActiveFiles = exports.findFilesByType = exports.findFilesByUploader = exports.deleteFile = exports.updateFile = exports.findByFileId = exports.findById = exports.createFile = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const fileCollection = () => db_1.db.collection("files");
const createFile = async (fileData) => {
    // Check if file with this fileId already exists
    const existingFile = await (0, exports.findByFileId)(fileData.fileId);
    if (existingFile) {
        throw new Error("File with this ID already exists");
    }
    const newFile = {
        ...fileData,
        uploadedBy: new mongodb_1.ObjectId(fileData.userId),
        uploaderType: fileData.uploaderType || "seeker", // Default to seeker if not specified
        active: true,
        isDeleted: false,
        uploadedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: fileData.metadata || {},
    };
    const result = await fileCollection().insertOne(newFile);
    return { ...newFile, _id: result.insertedId.toString() };
};
exports.createFile = createFile;
const findById = async (fileId) => {
    if (!mongodb_1.ObjectId.isValid(fileId)) {
        return null;
    }
    return await fileCollection().findOne({
        _id: new mongodb_1.ObjectId(fileId),
        isDeleted: false,
    });
};
exports.findById = findById;
const findByFileId = async (fileId) => {
    return await fileCollection().findOne({
        fileId,
        isDeleted: false,
    });
};
exports.findByFileId = findByFileId;
const updateFile = async (fileId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(fileId)) {
        return null;
    }
    const updatedFile = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await fileCollection().updateOne({ _id: new mongodb_1.ObjectId(fileId) }, updatedFile);
    return await (0, exports.findById)(fileId);
};
exports.updateFile = updateFile;
const deleteFile = async (fileId) => {
    if (!mongodb_1.ObjectId.isValid(fileId)) {
        return false;
    }
    const result = await fileCollection().updateOne({ _id: new mongodb_1.ObjectId(fileId) }, { $set: { isDeleted: true, active: false, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteFile = deleteFile;
const findFilesByUploader = async (uploaderId, uploaderType) => {
    if (!mongodb_1.ObjectId.isValid(uploaderId)) {
        return [];
    }
    return await fileCollection()
        .find({
        uploadedBy: new mongodb_1.ObjectId(uploaderId),
        uploaderType,
        isDeleted: false,
    })
        .sort({ uploadedAt: -1 })
        .toArray();
};
exports.findFilesByUploader = findFilesByUploader;
const findFilesByType = async (fileType, uploaderId) => {
    const query = {
        fileType,
        isDeleted: false,
        active: true,
    };
    if (uploaderId && mongodb_1.ObjectId.isValid(uploaderId)) {
        query.uploadedBy = new mongodb_1.ObjectId(uploaderId);
    }
    return await fileCollection()
        .find(query)
        .sort({ uploadedAt: -1 })
        .toArray();
};
exports.findFilesByType = findFilesByType;
const findActiveFiles = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, active: true, ...filters };
    const files = await fileCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ uploadedAt: -1 })
        .toArray();
    const total = await fileCollection().countDocuments(query);
    return { files, total };
};
exports.findActiveFiles = findActiveFiles;
const deactivateFile = async (fileId) => {
    if (!mongodb_1.ObjectId.isValid(fileId)) {
        return false;
    }
    const result = await fileCollection().updateOne({ _id: new mongodb_1.ObjectId(fileId) }, {
        $set: {
            active: false,
            updatedAt: new Date(),
        },
    });
    return result.modifiedCount > 0;
};
exports.deactivateFile = deactivateFile;
const activateFile = async (fileId) => {
    if (!mongodb_1.ObjectId.isValid(fileId)) {
        return false;
    }
    const result = await fileCollection().updateOne({ _id: new mongodb_1.ObjectId(fileId) }, {
        $set: {
            active: true,
            updatedAt: new Date(),
        },
    });
    return result.modifiedCount > 0;
};
exports.activateFile = activateFile;
const findFilesByMimeType = async (mimeType) => {
    return await fileCollection()
        .find({
        mimeType,
        isDeleted: false,
        active: true,
    })
        .sort({ uploadedAt: -1 })
        .toArray();
};
exports.findFilesByMimeType = findFilesByMimeType;
const getFileStatistics = async () => {
    const totalFiles = await fileCollection().countDocuments({
        isDeleted: false,
        active: true,
    });
    const sizeResult = await fileCollection()
        .aggregate([
        { $match: { isDeleted: false, active: true } },
        { $group: { _id: null, totalSize: { $sum: "$size" } } },
    ])
        .toArray();
    const totalSize = sizeResult.length > 0 ? sizeResult[0].totalSize : 0;
    const byTypeResult = await fileCollection()
        .aggregate([
        { $match: { isDeleted: false, active: true } },
        {
            $group: {
                _id: "$fileType",
                count: { $sum: 1 },
                totalSize: { $sum: "$size" },
            },
        },
    ])
        .toArray();
    const byType = byTypeResult.map(item => ({
        type: item._id,
        count: item.count,
        totalSize: item.totalSize,
    }));
    const byUploaderResult = await fileCollection()
        .aggregate([
        { $match: { isDeleted: false, active: true } },
        {
            $group: {
                _id: "$uploaderType",
                count: { $sum: 1 },
            },
        },
    ])
        .toArray();
    const byUploader = byUploaderResult.map(item => ({
        type: item._id,
        count: item.count,
    }));
    return {
        totalFiles,
        totalSize,
        byType,
        byUploader,
    };
};
exports.getFileStatistics = getFileStatistics;
const searchFiles = async (searchTerm, page = 1, limit = 10) => {
    const skip = (page - 1) * limit;
    const searchRegex = { $regex: searchTerm, $options: "i" };
    const query = {
        isDeleted: false,
        active: true,
        $or: [
            { fileName: searchRegex },
            { "metadata.description": searchRegex },
            { "metadata.tags": { $in: [searchRegex] } },
        ],
    };
    const files = await fileCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ uploadedAt: -1 })
        .toArray();
    const total = await fileCollection().countDocuments(query);
    return { files, total };
};
exports.searchFiles = searchFiles;
