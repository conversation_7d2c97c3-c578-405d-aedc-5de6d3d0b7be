"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findActiveCompanies = exports.findVerifiedCompanies = exports.verifyCompany = exports.searchCompanies = exports.findCompaniesBySize = exports.findCompaniesByIndustry = exports.findCompaniesByProvider = exports.getAllCompanies = exports.deleteCompany = exports.updateCompany = exports.findByNameAndProvider = exports.findById = exports.createCompany = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const companyCollection = () => db_1.db.collection("companies");
const createCompany = async (companyData) => {
    // Check if company with this name and providerId already exists
    const existingCompany = await (0, exports.findByNameAndProvider)(companyData.companyName, companyData.providerId);
    if (existingCompany) {
        throw new Error("Company with this name already exists for this provider");
    }
    const newCompany = {
        ...companyData,
        providerId: new mongodb_1.ObjectId(companyData.providerId),
        benefits: companyData.benefits || [],
        isVerified: false,
        isActive: true,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await companyCollection().insertOne(newCompany);
    return { ...newCompany, _id: result.insertedId.toString() };
};
exports.createCompany = createCompany;
const findById = async (companyId) => {
    if (!mongodb_1.ObjectId.isValid(companyId)) {
        return null;
    }
    return await companyCollection().findOne({
        _id: new mongodb_1.ObjectId(companyId),
        isDeleted: false,
    });
};
exports.findById = findById;
const findByNameAndProvider = async (companyName, providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    return await companyCollection().findOne({
        companyName,
        providerId: new mongodb_1.ObjectId(providerId),
        isDeleted: false,
    });
};
exports.findByNameAndProvider = findByNameAndProvider;
const updateCompany = async (companyId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(companyId)) {
        return null;
    }
    const updatedCompany = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await companyCollection().updateOne({ _id: new mongodb_1.ObjectId(companyId) }, updatedCompany);
    return await (0, exports.findById)(companyId);
};
exports.updateCompany = updateCompany;
const deleteCompany = async (companyId) => {
    if (!mongodb_1.ObjectId.isValid(companyId)) {
        return false;
    }
    const result = await companyCollection().updateOne({ _id: new mongodb_1.ObjectId(companyId) }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteCompany = deleteCompany;
const getAllCompanies = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, ...filters };
    const companies = await companyCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .toArray();
    const total = await companyCollection().countDocuments(query);
    return { companies, total };
};
exports.getAllCompanies = getAllCompanies;
const findCompaniesByProvider = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return [];
    }
    return await companyCollection()
        .find({
        providerId: new mongodb_1.ObjectId(providerId),
        isDeleted: false,
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findCompaniesByProvider = findCompaniesByProvider;
const findCompaniesByIndustry = async (industry) => {
    return await companyCollection()
        .find({
        isDeleted: false,
        isActive: true,
        industry: industry,
    })
        .sort({ companyName: 1 })
        .toArray();
};
exports.findCompaniesByIndustry = findCompaniesByIndustry;
const findCompaniesBySize = async (companySize) => {
    return await companyCollection()
        .find({
        isDeleted: false,
        isActive: true,
        companySize: companySize,
    })
        .sort({ companyName: 1 })
        .toArray();
};
exports.findCompaniesBySize = findCompaniesBySize;
const searchCompanies = async (searchTerm, page = 1, limit = 10) => {
    const skip = (page - 1) * limit;
    const searchRegex = { $regex: searchTerm, $options: "i" };
    const query = {
        isDeleted: false,
        isActive: true,
        $or: [
            { companyName: searchRegex },
            { description: searchRegex },
            { industry: searchRegex },
        ],
    };
    const companies = await companyCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ companyName: 1 })
        .toArray();
    const total = await companyCollection().countDocuments(query);
    return { companies, total };
};
exports.searchCompanies = searchCompanies;
const verifyCompany = async (companyId, isVerified = true) => {
    if (!mongodb_1.ObjectId.isValid(companyId)) {
        return null;
    }
    await companyCollection().updateOne({ _id: new mongodb_1.ObjectId(companyId) }, {
        $set: {
            isVerified,
            updatedAt: new Date(),
        },
    });
    return await (0, exports.findById)(companyId);
};
exports.verifyCompany = verifyCompany;
const findVerifiedCompanies = async () => {
    return await companyCollection()
        .find({
        isDeleted: false,
        isActive: true,
        isVerified: true,
    })
        .sort({ companyName: 1 })
        .toArray();
};
exports.findVerifiedCompanies = findVerifiedCompanies;
const findActiveCompanies = async () => {
    return await companyCollection()
        .find({
        isDeleted: false,
        isActive: true,
    })
        .sort({ companyName: 1 })
        .toArray();
};
exports.findActiveCompanies = findActiveCompanies;
