"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isProfileComplete = exports.getNextRequiredStep = exports.calculateProfileCompletion = void 0;
// Helper functions for profile completion calculation
const calculateProfileCompletion = (provider) => {
    const steps = provider.profileCompletionSteps;
    const totalSteps = 3;
    const completedSteps = Object.values(steps).filter(Boolean).length;
    return Math.round((completedSteps / totalSteps) * 100);
};
exports.calculateProfileCompletion = calculateProfileCompletion;
const getNextRequiredStep = (provider) => {
    const steps = provider.profileCompletionSteps;
    if (!steps.personalAccount)
        return "personalAccount";
    if (!steps.companyAddress)
        return "companyAddress";
    if (!steps.brandingVerification)
        return "brandingVerification";
    return null; // All steps completed
};
exports.getNextRequiredStep = getNextRequiredStep;
const isProfileComplete = (provider) => {
    const steps = provider.profileCompletionSteps;
    return steps.personalAccount && steps.companyAddress && steps.brandingVerification;
};
exports.isProfileComplete = isProfileComplete;
