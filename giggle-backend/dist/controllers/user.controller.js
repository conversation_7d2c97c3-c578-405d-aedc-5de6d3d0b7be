"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyUser = exports.sendVerificationEmail = exports.deleteUserProfile = exports.updateUserProfile = exports.getUserProfile = exports.registerUser = void 0;
const userService = __importStar(require("../services/user.service"));
const node_appwrite_1 = require("node-appwrite");
const registerUser = async (req, res) => {
    try {
        const user = req.user;
        const userData = {
            appwriteId: user.id,
            email: user.email,
            name: req.body.name || "",
            profile: req.body.profile || {},
            userType: req.user.type,
        };
        // Check if user is already registered
        const existingUser = await userService.findByAppwriteId(user.id);
        if (existingUser) {
            res.status(409).json({ error: "User already registered" });
            return;
        }
        // frontend--> sdk --> frontend(token) --> backend --> db
        const newUser = await userService.createUser(userData);
        res.status(201).json(newUser);
    }
    catch (error) {
        console.error("Error registering user:", error);
        res.status(500).json({ error: error.message || "Failed to register user" });
    }
};
exports.registerUser = registerUser;
const getUserProfile = async (req, res) => {
    try {
        const appwriteUser = req.user;
        const user = await userService.findByAppwriteId(appwriteUser.$id);
        if (!user) {
            res.status(404).json({ error: "User not found2" });
            return;
        }
        res.status(200).json(user);
    }
    catch (error) {
        console.error("Error fetching user profile:", error);
        res.status(500).json({ error: "Failed to fetch user profile" });
    }
};
exports.getUserProfile = getUserProfile;
const updateUserProfile = async (req, res) => {
    try {
        const appwriteUser = req.user;
        const updateData = req.body;
        // Validate update data
        if (Object.keys(updateData).length === 0) {
            res.status(400).json({ error: "No update data provided" });
            return;
        }
        const updatedUser = await userService.updateUser(appwriteUser.$id, updateData);
        if (!updatedUser) {
            res.status(404).json({ error: "User not found3" });
            return;
        }
        res.status(200).json(updatedUser);
    }
    catch (error) {
        console.error("Error updating user profile:", error);
        res.status(500).json({ error: "Failed to update user profile" });
    }
};
exports.updateUserProfile = updateUserProfile;
const deleteUserProfile = async (req, res) => {
    try {
        const appwriteUser = req.user;
        const success = await userService.deleteUser(appwriteUser.$id);
        if (!success) {
            res.status(404).json({ error: "User not found4" });
            return;
        }
        res.status(200).json({ message: "User deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting user:", error);
        res.status(500).json({ error: "Failed to delete user" });
    }
};
exports.deleteUserProfile = deleteUserProfile;
// NOT TO BE USED RN
const sendVerificationEmail = async (req, res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        res.status(401).json({ error: "Missing or invalid token" });
        return;
    }
    const token = authHeader.split(" ")[1];
    if (!token) {
        res.status(401).json({ error: "Missing token" });
        return;
    }
    const client = new node_appwrite_1.Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT)
        .setProject(process.env.APPWRITE_PROJECT_ID)
        .setSession(token);
    const account = new node_appwrite_1.Account(client);
    client.setJWT(token);
    try {
        const result = await account.createVerification("http://localhost:5000/api/users/verify-user");
        res.status(200).json({
            success: true,
            message: "Verification email sent. to " + result.userId,
            result,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || "Verification email failed.",
        });
    }
};
exports.sendVerificationEmail = sendVerificationEmail;
const verifyUser = async (req, res) => {
    const client = new node_appwrite_1.Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT)
        .setProject(process.env.APPWRITE_PROJECT_ID);
    const account = new node_appwrite_1.Account(client);
    const { userId, secret } = req.query;
    if (!secret || !userId) {
        return res.status(400).json({
            success: false,
            message: "Missing userId or secret in query parameters.",
        });
    }
    try {
        const result = await account.updateVerification(userId, secret);
        return res
            .status(200)
            .json({ success: true, message: "User verified successfully.", result });
    }
    catch (error) {
        console.error("Verification error:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Verification failed.",
        });
    }
};
exports.verifyUser = verifyUser;
