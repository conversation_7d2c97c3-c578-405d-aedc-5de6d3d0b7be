"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadResume = exports.updateSkills = exports.updateEducationDetails = exports.updateLocationPreferences = exports.updateBasicInfo = exports.getSeekerProfile = exports.updateSeekerProfile = exports.createSeekerProfile = void 0;
const seekerService = __importStar(require("../services/seeker.service"));
const constants_1 = require("../utils/constants");
const object_utils_1 = require("../utils/object-utils");
// Create seeker profile during onboarding
const createSeekerProfile = async (req, res) => {
    try {
        const user = req.user;
        const seekerData = {
            appwriteId: user.id,
            email: user.email,
            name: req.body.name,
            phoneNumber: req.body.phoneNumber,
            dateOfBirth: req.body.dateOfBirth ? new Date(req.body.dateOfBirth) : undefined,
            gender: req.body.gender,
            resumeLink: req.body.resumeLink,
            resumeFileId: req.body.resumeFileId,
            educationLevel: req.body.educationLevel || "high_school",
            employmentStatus: req.body.employmentStatus || "unemployed",
            education: req.body.education || [],
            workExperience: req.body.workExperience || [],
            skills: req.body.skills || [],
            languages: req.body.languages || [],
            certifications: req.body.certifications || [],
            portfolioLinks: req.body.portfolioLinks || [],
            expectedSalary: req.body.expectedSalary,
            availability: req.body.availability || {},
            preferences: req.body.preferences || {},
        };
        // Check if seeker profile already exists
        const existingSeeker = await seekerService.findByAppwriteId(user.id);
        if (existingSeeker) {
            res.status(409).json({ error: "Seeker profile already exists" });
            return;
        }
        const newSeeker = await seekerService.createSeeker(seekerData);
        res.status(201).json({
            success: true,
            message: "Seeker profile created successfully",
            data: newSeeker,
        });
    }
    catch (error) {
        console.error("Error creating seeker profile:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to create seeker profile"
        });
    }
};
exports.createSeekerProfile = createSeekerProfile;
// Update seeker profile step by step during onboarding
const updateSeekerProfile = async (req, res) => {
    try {
        const user = req.user;
        const updateData = req.body;
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, updateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Seeker profile updated successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error updating seeker profile:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update seeker profile"
        });
    }
};
exports.updateSeekerProfile = updateSeekerProfile;
// Get seeker profile
const getSeekerProfile = async (req, res) => {
    try {
        const user = req.user;
        const seeker = await seekerService.findByAppwriteId(user.id);
        if (!seeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: seeker,
        });
    }
    catch (error) {
        console.error("Error fetching seeker profile:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch seeker profile"
        });
    }
};
exports.getSeekerProfile = getSeekerProfile;
// Update basic info (name, date of birth, gender, phone)
const updateBasicInfo = async (req, res) => {
    try {
        const user = req.user;
        const { name, dateOfBirth, gender, phoneNumber } = req.body;
        const updateData = {
            name,
            dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
            gender,
            phoneNumber,
        };
        // Remove undefined values
        const cleanedUpdateData = (0, object_utils_1.removeUndefinedValues)(updateData);
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, cleanedUpdateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Basic information updated successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error updating basic info:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update basic information"
        });
    }
};
exports.updateBasicInfo = updateBasicInfo;
// Update location preferences
const updateLocationPreferences = async (req, res) => {
    try {
        const user = req.user;
        const { preferences } = req.body;
        const updateData = {
            preferences: {
                ...preferences,
            },
        };
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, updateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Location preferences updated successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error updating location preferences:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update location preferences"
        });
    }
};
exports.updateLocationPreferences = updateLocationPreferences;
// Update education details
const updateEducationDetails = async (req, res) => {
    try {
        const user = req.user;
        const { educationLevel, education, degree, specialization, completionYear, university } = req.body;
        const updateData = {
            educationLevel,
            education: education || [],
        };
        // If individual education fields are provided, create an education entry
        if (degree || specialization || completionYear || university) {
            const educationEntry = {
                institution: university || "",
                degree: degree || "",
                fieldOfStudy: specialization || "",
                startDate: new Date(),
                endDate: completionYear ? new Date(completionYear, constants_1.DATE_CONSTANTS.DEFAULT_GRADUATION_MONTH, constants_1.DATE_CONSTANTS.DEFAULT_GRADUATION_DAY) : undefined, // June 1st of completion year
                isCurrentlyStudying: !completionYear,
                grade: "",
                description: "",
            };
            updateData.education = [educationEntry];
        }
        // Remove undefined values
        const cleanedUpdateData = (0, object_utils_1.removeUndefinedValues)(updateData);
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, cleanedUpdateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Education details updated successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error updating education details:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update education details"
        });
    }
};
exports.updateEducationDetails = updateEducationDetails;
// Update skills
const updateSkills = async (req, res) => {
    try {
        const user = req.user;
        const { skills } = req.body;
        const updateData = {
            skills: skills || [],
        };
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, updateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Skills updated successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error updating skills:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update skills"
        });
    }
};
exports.updateSkills = updateSkills;
// Upload resume
const uploadResume = async (req, res) => {
    try {
        const user = req.user;
        const { resumeLink, resumeFileId } = req.body;
        const updateData = {
            resumeLink,
            resumeFileId,
        };
        // Remove undefined values
        const cleanedUpdateData = (0, object_utils_1.removeUndefinedValues)(updateData);
        const updatedSeeker = await seekerService.updateSeekerByAppwriteId(user.id, cleanedUpdateData);
        if (!updatedSeeker) {
            res.status(404).json({
                success: false,
                error: "Seeker profile not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Resume uploaded successfully",
            data: updatedSeeker,
        });
    }
    catch (error) {
        console.error("Error uploading resume:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to upload resume"
        });
    }
};
exports.uploadResume = uploadResume;
