"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGig = exports.deleteGig = exports.updateGig = exports.getProviderGigs = exports.createGig = void 0;
const gigService = __importStar(require("../services/gig.service"));
const providerService = __importStar(require("../services/provider.service"));
const constants_1 = require("../utils/constants");
const object_utils_1 = require("../utils/object-utils");
// Create a new gig (List Your Gig)
const createGig = async (req, res) => {
    try {
        const user = req.user;
        // Get provider details
        const provider = await providerService.findByAppwriteId(user.id);
        if (!provider) {
            res.status(404).json({
                success: false,
                error: "Provider profile not found"
            });
            return;
        }
        const gigData = {
            title: req.body.jobRole || "",
            description: req.body.otherRequirements || "",
            salary: {
                min: parseInt(req.body.salary) || 0,
                max: parseInt(req.body.salary) || 0,
                currency: "INR",
                period: "monthly"
            },
            numberOfOpenPositions: 1,
            location: req.body.latitude && req.body.longitude && req.body.address ? {
                latitude: parseFloat(req.body.latitude),
                longitude: parseFloat(req.body.longitude),
                address: req.body.address.trim()
            } : {
                latitude: constants_1.LOCATION_DEFAULTS.LATITUDE,
                longitude: constants_1.LOCATION_DEFAULTS.LONGITUDE,
                address: constants_1.LOCATION_DEFAULTS.ADDRESS
            },
            jobType: req.body.jobType || "full-time",
            jobTrait: req.body.remoteWork ? "remote" : "on-site",
            providerId: provider._id?.toString() || "",
            companyName: req.body.companyName || "",
            positionOffered: req.body.jobRole || "",
            minimumQualificationRequired: "",
            experience: {
                min: 0,
                max: 5,
                description: ""
            },
            specialization: [],
            facilities: (0, object_utils_1.parseFacilities)(req.body.facilities),
            minimumGiggleGrade: constants_1.GRADE_MAPPINGS[req.body.requiredGiggleGrade] || constants_1.GRADE_MAPPINGS.DEFAULT
        };
        const newGig = await gigService.createGig(gigData);
        res.status(201).json({
            success: true,
            message: "Gig created successfully",
            data: newGig,
        });
    }
    catch (error) {
        console.error("Error creating gig:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to create gig"
        });
    }
};
exports.createGig = createGig;
// Get provider's gigs
const getProviderGigs = async (req, res) => {
    try {
        const user = req.user;
        // Get provider details
        const provider = await providerService.findByAppwriteId(user.id);
        if (!provider) {
            res.status(404).json({
                success: false,
                error: "Provider profile not found"
            });
            return;
        }
        const gigs = await gigService.findGigsByProvider(provider._id?.toString() || "");
        res.status(200).json({
            success: true,
            data: gigs,
        });
    }
    catch (error) {
        console.error("Error fetching provider gigs:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch gigs"
        });
    }
};
exports.getProviderGigs = getProviderGigs;
// Update a gig
const updateGig = async (req, res) => {
    try {
        const { gigId } = req.params;
        const user = req.user;
        // Get provider details
        const provider = await providerService.findByAppwriteId(user.id);
        if (!provider) {
            res.status(404).json({
                success: false,
                error: "Provider profile not found"
            });
            return;
        }
        // Check if gig belongs to this provider
        const existingGig = await gigService.findById(gigId);
        if (!existingGig || existingGig.providerId.toString() !== provider._id?.toString()) {
            res.status(404).json({
                success: false,
                error: "Gig not found or access denied"
            });
            return;
        }
        const updateData = {
            title: req.body.jobRole,
            description: req.body.otherRequirements,
            salary: req.body.salary ? {
                min: parseInt(req.body.salary),
                max: parseInt(req.body.salary),
                currency: "INR",
                period: "monthly"
            } : undefined,
            jobType: req.body.jobType,
            jobTrait: req.body.remoteWork ? "remote" : "on-site",
            companyName: req.body.companyName,
            positionOffered: req.body.jobRole,
            facilities: req.body.facilities ? (0, object_utils_1.parseFacilities)(req.body.facilities) : undefined,
            minimumGiggleGrade: req.body.requiredGiggleGrade
                ? constants_1.GRADE_MAPPINGS[req.body.requiredGiggleGrade] || constants_1.GRADE_MAPPINGS.DEFAULT
                : undefined
        };
        // Remove undefined values
        const cleanedUpdateData = (0, object_utils_1.removeUndefinedValues)(updateData);
        const updatedGig = await gigService.updateGig(gigId, cleanedUpdateData);
        if (!updatedGig) {
            res.status(404).json({
                success: false,
                error: "Gig not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Gig updated successfully",
            data: updatedGig,
        });
    }
    catch (error) {
        console.error("Error updating gig:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to update gig"
        });
    }
};
exports.updateGig = updateGig;
// Delete a gig
const deleteGig = async (req, res) => {
    try {
        const { gigId } = req.params;
        const user = req.user;
        // Get provider details
        const provider = await providerService.findByAppwriteId(user.id);
        if (!provider) {
            res.status(404).json({
                success: false,
                error: "Provider profile not found"
            });
            return;
        }
        // Check if gig belongs to this provider
        const existingGig = await gigService.findById(gigId);
        if (!existingGig || existingGig.providerId.toString() !== provider._id?.toString()) {
            res.status(404).json({
                success: false,
                error: "Gig not found or access denied"
            });
            return;
        }
        const deleted = await gigService.deleteGig(gigId);
        if (!deleted) {
            res.status(404).json({
                success: false,
                error: "Gig not found"
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: "Gig deleted successfully",
        });
    }
    catch (error) {
        console.error("Error deleting gig:", error);
        res.status(500).json({
            success: false,
            error: error.message || "Failed to delete gig"
        });
    }
};
exports.deleteGig = deleteGig;
// Get a specific gig
const getGig = async (req, res) => {
    try {
        const { gigId } = req.params;
        const user = req.user;
        // Get provider details
        const provider = await providerService.findByAppwriteId(user.id);
        if (!provider) {
            res.status(404).json({
                success: false,
                error: "Provider profile not found"
            });
            return;
        }
        const gig = await gigService.findById(gigId);
        if (!gig || gig.providerId.toString() !== provider._id?.toString()) {
            res.status(404).json({
                success: false,
                error: "Gig not found or access denied"
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: gig,
        });
    }
    catch (error) {
        console.error("Error fetching gig:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch gig"
        });
    }
};
exports.getGig = getGig;
